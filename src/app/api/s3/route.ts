import { NextRequest, NextResponse } from "next/server";
import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} from "@aws-sdk/client-s3";
import { getSession } from "@auth0/nextjs-auth0";

// S3 client setup
const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "********************",
    secretAccessKey:
      process.env.AWS_SECRET_ACCESS_KEY ||
      "B29COJwp4Fopx0Xoz+2yTJKFacZOD+so48yZXASM",
  },
});

const BUCKET_NAME = process.env.S3_BUCKET_NAME || "subconscious-ai-uploads";

export async function POST(request: NextRequest) {
  const res = new NextResponse();
  try {
    // Get user session
    const session = await getSession(request, res);
    if (!session || !session.user || !session.user.sub) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const userId = session.user.sub.replace("|", "_"); // Replace pipe for safe filenames

    const formData = await request.formData();
    const file = formData.get("image") as File;

    if (!file) {
      return NextResponse.json({ error: "No image provided" }, { status: 400 });
    }

    // Generate a unique filename using user ID and timestamp
    const fileExtension = file.name.split(".").pop();
    const timestamp = Date.now();
    // Use slash after userId for folder structure
    const uniqueFilename = `${userId}/concept_${timestamp}.${fileExtension}`;

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Upload to S3
    const params = {
      Bucket: BUCKET_NAME,
      Key: uniqueFilename,
      Body: buffer,
      ContentType: file.type,
    };

    await s3Client.send(new PutObjectCommand(params));

    const imageUrl = `https://${BUCKET_NAME}.s3.amazonaws.com/${uniqueFilename}`;

    return NextResponse.json(
      {
        success: true,
        message: "Image uploaded successfully",
        imageUrl,
        key: uniqueFilename,
      },
      res
    );
  } catch (error) {
    console.error("Error uploading to S3:", error);
    return NextResponse.json(
      { error: "Failed to upload image" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  const res = new NextResponse();
  try {
    // Optional: Add session check for deletion as well for security
    const session = await getSession(request, res);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    // We might want to verify if the user owns the key they are trying to delete
    // For now, we proceed if the user is authenticated.

    const { searchParams } = new URL(request.url);
    const key = searchParams.get("key");

    if (!key) {
      return NextResponse.json({ error: "No key provided" }, { status: 400 });
    }

    const params = {
      Bucket: BUCKET_NAME,
      Key: key,
    };

    await s3Client.send(new DeleteObjectCommand(params));

    return NextResponse.json(
      {
        success: true,
        message: "Image deleted successfully",
      },
      res
    );
  } catch (error) {
    console.error("Error deleting from S3:", error);
    return NextResponse.json(
      { error: "Failed to delete image" },
      { status: 500 }
    );
  }
}
