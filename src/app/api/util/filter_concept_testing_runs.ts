import { Run } from "@/app/_components/_experiments/types";

function filterConceptTestingRuns(runs: Run[] | undefined): Run[] {
  if (!runs || runs.length === 0) {
    return [];
  }

  return runs
    .filter((run: Run) => run.experiment_type === "concept_testing")
    .map((run: Run) => {
      return {
        id: run.id,
        name: run.name,
        state: run.state,
        failed: run.state === "finished" && !("r_squared" in run),
        r_squared: run.r_squared ?? 0,
        sample_size: run.sample_size ?? 0,
        total_number_of_tasks: run.total_number_of_tasks ?? 0,
        tasks_per_respondent: run.tasks_per_respondent ?? 0,
        expr_llm_model: run.expr_llm_model ?? "gpt4",
        created_at: new Date(run.created_at),
        question: run.why_prompt ?? "",
        task_count: run.total_number_of_tasks ?? 0,
        confidence: run.confidence_level,
        survey_prompt: run.survey_prompt ?? "",
        amce_filename: run.amce_filename,
        is_private: run.is_private ?? false,
        experiment_type: run.experiment_type,
      };
    });
}

export default filterConceptTestingRuns;
