"use client";
import { useState, useEffect } from "react";
import { withPageAuthRequired } from "@auth0/nextjs-auth0/client";
import ConceptTestingContext, {
  QuestionWithScale,
  ConceptTestingContextProvider,
} from "../_components/_concept-testing/ConceptTestingContext";
import countries from "../../../public/data/countries.json";
import {
  Country,
  PopulationTraits,
  DisplayTrait,
  DisplayAttribute,
  BrandAttributeCombination,
  FileState,
  LLMModel,
  Persona,
  TraitCategory,
} from "../_components/_ideation/objects";
import ImageDescriptionStep from "../_components/_concept-testing/ImageDescriptionStep";
import QuestionsStep from "../_components/_concept-testing/QuestionsStep";
import WhenWhereStep from "../_components/_concept-testing/WhenWhereStep";
import FinalReviewStep from "../_components/_concept-testing/FinalReviewStep";
import WhoStep from "../_components/_concept-testing/WhoStep";
import { v4 as uuidv4 } from "uuid";
import ProgressIndicator from "../_components/_ideation/ProgressIndicator";
import ConceptTestingProviders from "../_components/_concept-testing/ConceptTestingProviders";
import traitValues from "../_components/_ideation/traits.js";
import { Header } from "../_components/_ui/NotificationCenter";

// Transform traits helper function
function transformTraits(traits: any): DisplayTrait[] {
  const result: DisplayTrait[] = [];
  Object.entries(traits).forEach(([category, categoryTraits]) => {
    if (typeof categoryTraits === "object" && categoryTraits !== null) {
      Object.entries(categoryTraits as Record<string, string[]>).forEach(
        ([trait, values]) => {
          result.push({
            title: trait.charAt(0).toUpperCase() + trait.slice(1),
            active: false,
            values: Array.isArray(values)
              ? values.map((item) =>
                  typeof item === "string"
                    ? item.charAt(0).toUpperCase() + item.slice(1)
                    : String(item)
                )
              : [],
            category: category as TraitCategory,
          });
        }
      );
    }
  });
  return result;
}

export default withPageAuthRequired(function ConceptTestingPage() {
  // Concept testing specific state
  const [currStep, setCurrStep] = useState(0);
  const [image, setImage] = useState<File | null>(null);
  const [imageS3Url, setImageS3Url] = useState<string | null>(null);
  const [imageS3Key, setImageS3Key] = useState<string | null>(null);
  const [description, setDescription] = useState("");
  const [questions, setQuestions] = useState<QuestionWithScale[]>([
    { id: uuidv4(), text: "" },
  ]);

  // When/Where state
  const [when, setWhen] = useState(new Date().getFullYear().toString());
  const [where, setWhere] = useState<Country>(countries[0]);
  const [selectedState, setSelectedState] = useState<string | null>(null);

  // Who component state
  // Step 1: Population demographic traits (age, income, gender, etc.)
  const [populationTraits, setPopulationTraits] = useState<PopulationTraits>({
    state: null,
    age: [18, 95],
    household_income: [0, 371000],
    gender: ["Female", "Male"],
    education_level: [
      "High School Diploma",
      "High School but no diploma",
      "Some College",
      "Less than high school",
      "Bachelors",
      "Masters",
      "Associates",
      "PhD",
    ],
    number_of_children: ["0", "1", "2", "4+", "3"],
    racial_group: [
      "White",
      "African American",
      "Mixed race",
      "Asian or Pacific Islander",
      "Other race",
    ],
  });

  // Step 3: Specialist display traits (from traitValues.js)
  const [displayTraits, setDisplayTraits] = useState<DisplayTrait[]>([]);

  const [displayAttributes, setDisplayAttributes] = useState<
    DisplayAttribute[]
  >([]);
  const [
    realWorlBrandAttributeCombinations,
    setRealWorldBrandAttributeCombinations,
  ] = useState<BrandAttributeCombination[]>([]);
  const [validatedQuestions, setValidatedQuestions] = useState<Set<string>>(
    new Set()
  );
  const [personas, setPersonas] = useState<Persona[]>([]);
  const [specialistTraits, setSpecialistTraits] = useState<
    Record<string, DisplayTrait[]>
  >({});
  const [fileState, setFileState] = useState<FileState>({
    file: null,
    data: null,
    error: null,
  });
  const [selectedLlmModel, setSelectedLlmModel] = useState<LLMModel>({
    name: "gpt4",
  });
  const [selectedSection, setSelectedSection] = useState<
    "characteristics" | "personas"
  >("characteristics");
  const [productExists, setProductExists] = useState<boolean>(false);
  const [activeSpecialist, setActiveSpecialist] = useState<string>("");

  // Previous step tracking (from ideation pattern)
  const [previousStep, setPreviousStep] = useState<number>(-1);

  // WHO step state management (for context)
  const [whoStep, setWhoStep] = useState<"first" | "second" | "third">("first");

  // Step management
  const [steps, setSteps] = useState([
    { name: "Image & Description", status: "current" },
    { name: "Questions", status: "upcoming" },
    { name: "When/Where", status: "upcoming" },
    { name: "Who", status: "upcoming" },
    { name: "Final Review", status: "upcoming" },
  ]);

  // Initialize displayTraits for Who step (step 3 - specialist traits)
  useEffect(() => {
    if (currStep === 3 && displayTraits.length === 0) {
      try {
        const transformed = transformTraits(traitValues);
        setDisplayTraits(transformed);
      } catch (error) {
        console.error("Error transforming traits for specialist step:", error);
      }
    }
  }, [currStep, displayTraits.length]);

  // Step completion handlers
  function completeStep(stepIdx: number) {
    setSteps((prev) => {
      const updated = prev.map((step, idx) => {
        if (idx < stepIdx) return { ...step, status: "complete" };
        if (idx === stepIdx) return { ...step, status: "current" };
        return { ...step, status: "upcoming" };
      });
      return updated;
    });
    setCurrStep(stepIdx);
  }

  // Navigation handlers
  const handleBack = () => {
    setPreviousStep(currStep);
    const newStep = currStep - 1;

    // Set WHO step when navigating back to WHO step (step 3)
    if (newStep === 3) {
      if (where?.name !== "United States of America (USA)") {
        setWhoStep("third");
      } else {
        // For USA, go to third step if we have population data, otherwise first
        setWhoStep("third");
      }
    }

    completeStep(newStep);
  };
  const handleNext = () => {
    setPreviousStep(currStep);
    completeStep(currStep + 1);
  };

  // Handle when/where completion to set WHO step appropriately
  const handleWhenWhereComplete = () => {
    if (where?.name !== "United States of America (USA)") {
      setWhoStep("third");
    }
    setPreviousStep(currStep);
    handleNext();
  };

  // Derive question from description or first question for API compatibility
  const derivedQuestion =
    description || (questions.length > 0 && questions[0].text) || "";

  return (
    <ConceptTestingContextProvider
      value={{
        // Concept-testing specific fields
        image,
        setImage,
        imageS3Url,
        setImageS3Url,
        imageS3Key,
        setImageS3Key,
        description,
        setDescription,
        questions,
        setQuestions,
        currStep,
        setCurrStep: (step: number) => completeStep(step),
        steps,
        setSteps,
        previousStep,
        setPreviousStep,

        // When/Where fields
        when,
        setWhen,
        where,
        setWhere,
        selectedState,
        setSelectedState,

        // Who component fields
        populationTraits,
        setPopulationTraits,
        displayTraits,
        setDisplayTraits,
        displayAttributes,
        setDisplayAttributes,
        realWorlBrandAttributeCombinations,
        setRealWorldBrandAttributeCombinations,
        selectedLlmModel,
        setSelectedLlmModel,
        validatedQuestions,
        setValidatedQuestions,
        personas,
        setPersonas,
        specialistTraits,
        setSpecialistTraits,
        selectedSection,
        setSelectedSection,
        productExists,
        setProductExists,
        fileState,
        setFileState,
        question: derivedQuestion,
        activeSpecialist,
        setActiveSpecialist,
      }}
    >
      <ConceptTestingProviders selectedState={selectedState}>
        <div className="z-10 py-8 px-10 w-full flex flex-col font-inter min-h-screen bg-background">
          <div className="mx-auto w-full">
            {/* Header */}
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-3xl font-semibold text-text-dark">
                Create Concept
              </h1>
              <div className="flex h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm">
                <Header />
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-10 flex justify-center w-full">
              <ProgressIndicator steps={steps} setCurrStep={completeStep} />
            </div>
          </div>

          {/* Content */}
          <div className="flex items-center justify-center pb-4 flex-1">
            <div className="w-full max-w-4xl mx-auto flex flex-col h-full">
              {currStep === 0 && (
                <ImageDescriptionStep onComplete={handleNext} />
              )}
              {currStep === 1 && (
                <QuestionsStep onComplete={handleNext} onBack={handleBack} />
              )}
              {currStep === 2 && (
                <WhenWhereStep
                  onComplete={handleWhenWhereComplete}
                  onBack={handleBack}
                />
              )}
              {currStep === 3 && (
                <WhoStep
                  onComplete={handleNext}
                  onBack={handleBack}
                  whoStep={whoStep}
                  setWhoStep={setWhoStep}
                />
              )}
              {currStep === 4 && <FinalReviewStep onBack={handleBack} />}
            </div>
          </div>
        </div>
      </ConceptTestingProviders>
    </ConceptTestingContextProvider>
  );
});
