/* eslint-disable prettier/prettier */
//@ts-nocheck
"use client";
import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useRouter } from "next/navigation";
import Loading from "../../_components/_ui/Loading";
import ConceptTestingOverviewCard from "../../_components/_concept-testing/ConceptTestingOverviewCard";
import { Run } from "../../_components/_experiments/types";
import { useUser, withPageAuthRequired } from "@auth0/nextjs-auth0/client";
import SessionContext from "../../_components/_util/SessionContext";
import filterConceptTestingRuns from "../../api/util/filter_concept_testing_runs";
import {
  AlarmClock,
  ChevronUp,
  RefreshCw,
  Search,
  FileQuestion,
} from "lucide-react";
import { Header } from "../../_components/_ui/NotificationCenter";
import CustomEventSource from "../../utils/CustomEventSource";
import * as Sentry from "@sentry/react";
import Confetti from "react-confetti";
import Paginator from "../../_components/_ui/Paginator";
import axios from "axios";

const fetcher = async (uri: string, userID: string | null) => {
  try {
    const response = await fetch(uri, {
      method: "POST",
      body: JSON.stringify({ userID: userID }),
    });
    if (!response.ok) {
      const error = new Error("Failed to fetch data");
      Sentry.captureException(error, {
        extra: { uri, userID, status: response.status },
      });
      throw error;
    }
    const data = await response.json();
    return data;
  } catch (error) {
    Sentry.captureException(error, {
      extra: { uri, userID },
    });
    throw error;
  }
};

const ITEMS_PER_PAGE = 10;

export default withPageAuthRequired(
  function ConceptTestingResultsPage() {
    const { user } = useUser();
    const router = useRouter();
    const [collapsed, setCollapsed] = useState(true);
    const { runs, setRuns } = useContext(SessionContext);
    const [eventSource, setEventSource] = useState(null);

    const [localRuns, setLocalRuns] = useState<Run[] | undefined>(undefined);
    const [activeTab, setActiveTab] = useState("myConceptTests");
    const [filteredData, setFilteredData] = useState<any[]>([]);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [connectionActive, setConnectionActive] = useState(true);

    const [selectedDropdownItem, setSelectedDropdownItem] =
      useState("All Concept Tests");

    const [isLoading, setIsLoading] = useState(true);
    const [connectionError, setConnectionError] = useState(false);
    const [accessToken, setAccessToken] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState("");
    const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");

    const [showConfetti, setShowConfetti] = useState(false);
    const [confettiExperimentId, setConfettiExperimentId] = useState<
      string | null
    >(null);

    const [queueCount, setQueueCount] = useState(0);

    useEffect(() => {
      const timer = setTimeout(() => {
        setDebouncedSearchQuery(searchQuery);
      }, 300);

      return () => clearTimeout(timer);
    }, [searchQuery]);

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const fetchAndUpdateData = async () => {
      setIsLoading(true);

      if (user?.sub) {
        try {
          const fetchRuns = await fetcher("/api/runs", user?.sub);

          // Filter for concept testing experiments only
          const conceptTestingRuns = fetchRuns.filter(
            (run: Run) => run.experiment_type === "concept_testing"
          );

          setLocalRuns(conceptTestingRuns);

          const filteredRuns = filterConceptTestingRuns(conceptTestingRuns);
          setRuns(filteredRuns);

          let pendingExpRunId =
            localStorage.getItem("pendingConceptTestRunId") || "";
          let recentExpId = filteredRuns.find(
            (run) => run.state == "finished"
          )?.id;

          // Check if we should show confetti for a newly finished concept test
          if (
            pendingExpRunId &&
            recentExpId &&
            pendingExpRunId === recentExpId
          ) {
            localStorage.setItem(
              "runningConceptTests",
              Math.max(
                0,
                JSON.parse(localStorage.getItem("runningConceptTests") || "0") -
                  1
              )
            );
          }

          let runningConceptTests = JSON.parse(
            localStorage.getItem("runningConceptTests")
          );

          // Find newly started concept tests
          const newRunningConceptTest = filteredRuns.find(
            (run) => run.state === "running"
          );

          // Update pendingExpRunId
          const previousPendingId = pendingExpRunId;
          pendingExpRunId = newRunningConceptTest?.id;
          localStorage.setItem(
            "pendingConceptTestRunId",
            pendingExpRunId || ""
          );

          // Show confetti for newly started concept tests
          if (pendingExpRunId && pendingExpRunId !== previousPendingId) {
            // Get the list of concept tests that have already shown confetti
            const shownConfettiConceptTests = JSON.parse(
              localStorage.getItem("shownConfettiConceptTests") || "[]"
            );

            // If this concept test hasn't shown confetti yet, show it
            if (!shownConfettiConceptTests.includes(pendingExpRunId)) {
              setConfettiExperimentId(pendingExpRunId);
              setShowConfetti(true);

              // Add this concept test to the list of concept tests that have shown confetti
              shownConfettiConceptTests.push(pendingExpRunId);
              localStorage.setItem(
                "shownConfettiConceptTests",
                JSON.stringify(shownConfettiConceptTests)
              );
            }
          }

          const queueResponse = await axios.get("/api/tasks");
          const queueData = queueResponse.data;
          setQueueCount(queueData.length);
        } catch (error) {
          console.error("Error fetching concept testing runs:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    useEffect(() => {
      const fetchToken = async () => {
        try {
          const response = await fetch("/api/token");
          if (!response.ok) {
            throw new Error("Network response was not ok");
          }
          const data = await response.json();
          setAccessToken(data.accessToken);
        } catch (error) {
          console.error("Failed to fetch access token:", error);
          Sentry.captureException(error);
        }
      };

      fetchToken();
    }, []);

    useEffect(() => {
      const setupEventSource = () => {
        if (user && accessToken) {
          const newEventSource = new CustomEventSource(
            `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/notifications/events`,
            {
              headers: {
                Authorization: `Bearer ${accessToken}`,
              },
            }
          );

          const eventHandler = (event) => {
            try {
              const data = JSON.parse(event.data);
            } catch (error) {
              console.error("Failed to parse event data:", error);
              Sentry.captureException(error, {
                extra: {
                  message: "Malformed event data",
                  data: event.data,
                },
              });
            }
            fetchAndUpdateData();
          };

          newEventSource.addEventListener("started", eventHandler);
          newEventSource.addEventListener("finished", eventHandler);
          newEventSource.addEventListener("crashed", eventHandler);

          newEventSource.addEventListener("connection-status", (event) => {
            const status = JSON.parse(event.data);
            setConnectionActive(!status.reconnecting);
          });

          newEventSource.onerror = (error) => {
            Sentry.captureException(error, {
              extra: { message: "EventSource failed. Reconnecting..." },
            });
            setTimeout(() => {
              setupEventSource(); // Retry connecting
            }, 3000);
          };

          setEventSource(newEventSource);
        }
      };

      if (user && accessToken) {
        fetchAndUpdateData();
        setupEventSource();
      }

      return () => {
        if (eventSource) {
          eventSource.close();
        }
      };
    }, [user, accessToken]);

    const searchConceptTests = (query: string, conceptTests: Run[]): Run[] => {
      if (!conceptTests?.length) return conceptTests;
      if (!query.trim()) return conceptTests;

      // Convert query to lowercase and split into terms for multi-word search
      const searchTerms = query.toLowerCase().trim().split(/\s+/);

      return conceptTests.filter((conceptTest) => {
        // Create a searchable string combining all relevant fields
        const searchableText = [
          // Basic info
          conceptTest.name,
          conceptTest.id,
          conceptTest.state,
          conceptTest.created_at,
          conceptTest.confidence,
          conceptTest.question,
          conceptTest.survey_prompt,

          // Numeric values converted to string
          conceptTest.r_squared?.toString(),
          conceptTest.sample_size?.toString(),
          conceptTest.total_number_of_tasks?.toString(),
          conceptTest.task_count?.toString(),

          // Status info
          conceptTest.failed ? "failed" : "success",
          conceptTest.is_private ? "private" : "public",
        ]
          .filter(Boolean) // Remove null/undefined values
          .join(" ")
          .toLowerCase();

        return searchTerms.every((term) => searchableText.includes(term));
      });
    };

    const filterConceptTests = useCallback(
      (runs: Run[]) => {
        if (!runs) return [];

        let filtered = searchConceptTests(debouncedSearchQuery, runs);

        if (activeTab === "communityConceptTests") {
          filtered = filtered.filter((run) => !run.is_private);
        } else {
          if (selectedDropdownItem === "Your Public Concept Tests") {
            filtered = filtered.filter((run) => !run.is_private);
          } else if (selectedDropdownItem === "Private Concept Tests") {
            filtered = filtered.filter((run) => run.is_private);
          }
        }

        return filtered;
      },
      [
        activeTab,
        selectedDropdownItem,
        debouncedSearchQuery,
        searchConceptTests,
      ]
    );

    const handleTabClick = useCallback((tab: string) => {
      setActiveTab(tab);
      setCurrentPage(1);
    }, []);

    const handleShowAllConceptTests = useCallback(() => {
      setCollapsed(!collapsed);
    }, [collapsed]);

    const handleDropdownItemClick = useCallback((item: string) => {
      setSelectedDropdownItem(item);
      setCollapsed(true);
      setCurrentPage(1);
    }, []);

    const filteredLocalRuns = useMemo(
      () => (localRuns ? filterConceptTests(localRuns) : []),
      [localRuns, filterConceptTests]
    );

    const filteredRuns = useMemo(
      () => filterConceptTests(runs),
      [runs, filterConceptTests]
    );

    const handleRefreshClick = useCallback(() => {
      fetchAndUpdateData();
    }, [fetchAndUpdateData]);

    const quickRefresh = useCallback(() => {
      return fetchAndUpdateData();
    }, [fetchAndUpdateData]);

    const handlePrivacyToggle = (id: string, isPrivate: boolean) => {
      setLocalRuns((prevLocalRuns) =>
        prevLocalRuns.map((run) =>
          run.id === id ? { ...run, is_private: isPrivate } : run
        )
      );
      setRuns((prevRuns) =>
        prevRuns.map((run) =>
          run.id === id ? { ...run, is_private: isPrivate } : run
        )
      );
    };

    // ------------Pagination----------------------
    const paginatedData = filteredData.slice(
      (currentPage - 1) * ITEMS_PER_PAGE,
      currentPage * ITEMS_PER_PAGE
    );
    useEffect(() => {
      if (runs.length > 0) {
        setFilteredData(runs);
      }
    }, [runs, paginatedData]);

    const handlePageChange = (page: number) => {
      setCurrentPage(page);
    };

    const ReplayTutorials = () => {
      if (window.pendo && window.pendo.isReady) {
        window.pendo.showGuideById("kpKqPpqjYOEUf_o5hXy9GDtUeRM");
      }
    };

    useEffect(() => {
      if (showConfetti) {
        const timer = setTimeout(() => {
          setShowConfetti(false);
          setConfettiExperimentId(null);
        }, 2500);
        return () => clearTimeout(timer);
      }
    }, [showConfetti]);

    return (
      <div className="z-10 py-8 px-10 w-full flex flex-col font-inter">
        {showConfetti && <Confetti gravity={0.3} />}

        <div className="flex justify-between">
          <h1 className="text-text-dark font-medium text-3xl pb-6">
            Concept Testing Results
          </h1>
          <div className="flex items-center gap-2">
            <div
              className="flex cursor-pointer h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm"
              onClick={handleRefreshClick}
            >
              <RefreshCw className="w-7 h-6 text-[#868e90]" />
            </div>
            <button
              onClick={ReplayTutorials}
              className="flex h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm"
            >
              Show Tutorial
            </button>
            <div className="flex h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm">
              <Header />
            </div>
          </div>
        </div>

        <div className="flex w-full justify-between items-center">
          <div className="flex border-b relative border-[#D0D5DD] h-8 text-sm font-semibold">
            <div
              className={`cursor-pointer ${
                activeTab === "myConceptTests"
                  ? "border-b text-[#312E81] border-[#312E81]"
                  : "text-[#667085]"
              }`}
              onClick={() => handleTabClick("myConceptTests")}
            >
              Your Concept Tests
            </div>
          </div>
          {activeTab === "myConceptTests" && (
            <div className="min-w-fit flex flex-col relative cursor-pointer">
              <div
                className="flex items-center justify-between gap-3 bg-white border-[#D0D5DD] w-full border px-3 py-2 boxshadow-allexps rounded-lg"
                onClick={handleShowAllConceptTests}
              >
                {selectedDropdownItem}
                <ChevronUp
                  className={`h-6 w-6 text-chevron transition-all duration-300 ${
                    collapsed ? "rotate-180" : "rotate-0"
                  }`}
                />
              </div>
              {!collapsed && (
                <div className="flex w-full flex-col px-[6px] py-1 absolute boxshadow-dropdown top-12 rounded-lg z-10 bg-white">
                  <div
                    className={`flex justify-start rounded-md w-full px-2 py-3 cursor-pointer ${
                      selectedDropdownItem === "All Concept Tests"
                        ? "bg-[#EFF0F5]"
                        : ""
                    }`}
                    onClick={() => handleDropdownItemClick("All Concept Tests")}
                  >
                    All Concept Tests
                  </div>
                  <div
                    className={`flex justify-start px-2 w-full py-3 cursor-pointer ${
                      selectedDropdownItem === "Your Public Concept Tests"
                        ? "bg-[#EFF0F5]"
                        : ""
                    }`}
                    onClick={() =>
                      handleDropdownItemClick("Your Public Concept Tests")
                    }
                  >
                    Your Public Concept Tests
                  </div>
                  <div
                    className={`flex justify-start px-2 w-full py-3 cursor-pointer ${
                      selectedDropdownItem === "Private Concept Tests"
                        ? "bg-[#EFF0F5]"
                        : ""
                    }`}
                    onClick={() =>
                      handleDropdownItemClick("Private Concept Tests")
                    }
                  >
                    Private Concept Tests
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {isLoading ? (
          <div className="flex-1 flex items-center justify-center min-h-[calc(100vh-250px)]">
            <Loading />
          </div>
        ) : (
          <>
            {localRuns !== undefined &&
              localRuns.length === 0 &&
              runs.length === 0 &&
              queueCount === 0 && (
                <div className="flex items-center justify-center mt-4">
                  <div className="flex flex-col gap-4 w-full py-24 items-center text-center border border-card-border rounded-xl bg-white">
                    <FileQuestion className="w-16 h-16 text-gray-400" />
                    <p className="text-text-dark font-medium text-2xl">
                      You have not created any concept tests yet.
                    </p>
                    <button
                      className="flex bg-[#504D9A] text-white font-inter font-medium text-lg px-4 py-2 rounded-xl items-center justify-center"
                      onMouseDown={() => {
                        router.push(`/concept-testing`);
                      }}
                    >
                      <div className="flex w-fit gap-2 items-center px-2">
                        <p className="whitespace-nowrap">
                          Create your first Concept Test
                        </p>
                      </div>
                    </button>
                  </div>
                </div>
              )}

            {!isLoading &&
              runs.length === 0 &&
              localRuns !== undefined &&
              localRuns.length !== 0 && (
                <div className="flex flex-col gap-4 w-full justify-center items-center text-center">
                  <p className="font-roboto text-text-dark text-xl font-normal">
                    Please wait while your concept test finishes executing.
                  </p>
                </div>
              )}

            {localRuns && localRuns.length >= 5 && (
              <div className="relative flex items-center w-full mt-5 mb-4">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search by concept test name, description, ID (partial ok), Date, Respondents, state (finished/failed)..."
                  className="w-full px-4 py-2 pl-10 border rounded-lg
                focus:outline-none focus:ring-2 focus:ring-primary-dark
                bg-white transition-all duration-200"
                  spellCheck="false"
                  autoComplete="off"
                />
                <Search className="absolute left-3 w-4 h-4 text-gray-400" />
              </div>
            )}

            {queueCount > 0 && (
              <div className="border mt-4 border-blue-500 text-blue-700 rounded-md px-3 py-2 bg-blue-100 font-semibold mb-2 flex flex-row items-center gap-3">
                <div>
                  <AlarmClock />
                </div>
                <div className="flex flex-col">
                  <div className="py-1">
                    Concept Tests in Queue ({queueCount}). It will start
                    shortly...
                  </div>
                </div>
              </div>
            )}

            <div className="mb-4 mt-4">
              {filteredLocalRuns && filteredLocalRuns.length !== 0 && (
                <div className="flex flex-col w-full gap-4">
                  {filteredLocalRuns
                    .filter(
                      (run: Run) => run.state === "running" && !run.question
                    )
                    .map((run: Run) => (
                      <div key={run.id}>
                        <ConceptTestingOverviewCard
                          run={run}
                          runCount={filteredLocalRuns.length}
                          onPrivacyToggle={handlePrivacyToggle}
                          quickRefresh={quickRefresh}
                        />
                      </div>
                    ))}
                </div>
              )}
            </div>

            {filteredRuns.length !== 0 && (
              <div className="flex flex-col w-full gap-4">
                {filteredRuns
                  .filter((run: Run) => run.state !== "running")
                  .slice(
                    (currentPage - 1) * ITEMS_PER_PAGE,
                    currentPage * ITEMS_PER_PAGE
                  )
                  .map((run: Run) => {
                    return (
                      <div key={run.id}>
                        <ConceptTestingOverviewCard
                          run={run}
                          runCount={filteredRuns.length}
                          onPrivacyToggle={handlePrivacyToggle}
                          quickRefresh={quickRefresh}
                        />
                      </div>
                    );
                  })}
              </div>
            )}

            {filteredRuns.length > ITEMS_PER_PAGE && (
              <div className="flex justify-center items-center mt-4">
                <Paginator
                  page={currentPage}
                  setPage={handlePageChange}
                  totalPage={Math.ceil(filteredRuns.length / ITEMS_PER_PAGE)}
                />
              </div>
            )}
          </>
        )}
      </div>
    );
  },
  {
    returnTo: "/concepts/results",
  }
);
