/* eslint-disable prettier/prettier */
"use client";

import React, { useState } from "react";
import { SparklesIcon } from "@heroicons/react/24/outline";
import Loading from "../_ui/Loading";
import Button from "../_ui/Button";
import Link from "next/link";

export interface TabDetailProps {
  tabContentName: string;
  tabTitle: string;
  contentDetails: any;
}

export interface ActionDetailProps {
  buttonName: string;
  buttonActionLink: string;
}

interface CustomActionProps {
  customButton: any;
}

interface OverviewCardProps {
  collapsed: boolean;
  tabDetails: TabDetailProps[];
  loadingCondition?: any;
  actionDetails?: ActionDetailProps[];
  customActionDetails?: CustomActionProps[];
  cardDetailTop: any;
  isTimerActive?: boolean;
}

const tabContentStyle = "cursor-pointer flex items-center py-2 px-4";
const activeTabStyle = "shadow-md text-primary bg-white rounded-md";
const inactiveTabStyle = "text-subtitle";

const OverviewCard = ({
  cardDetailTop,
  collapsed,
  customActionDetails,
  tabDetails,
  actionDetails,
  loadingCondition,
  isTimerActive = false,
}: OverviewCardProps) => {
  const [tabContent, setTabContent] = useState(tabDetails[0].tabContentName);
  const tabContentComponent = (
    <>
      <div className="flex justify-between">
        <div className="flex border border-card-border rounded-md p-1 bg-background">
          {tabDetails.map((tabDetail, idx) => {
            const { tabContentName, tabTitle } = tabDetail;
            return (
              <div
                key={idx}
                className={`${tabContentStyle} ${tabContent === tabContentName ? activeTabStyle : inactiveTabStyle}`}
                onMouseDown={() => setTabContent(tabContentName)}
              >
                <span>{tabTitle}</span>
              </div>
            );
          })}
        </div>
        <div className="flex justify-end">
          {/* Showcase overview has simple button components which makes up the actionDetails */}
          {/* If we have specific conditions that needs to be met for the buttons to be interactive, then we use customActionDetail */}
          {actionDetails &&
            actionDetails.map((actionDetail) => {
              const { buttonName, buttonActionLink } = actionDetail;
              return (
                <div key={buttonName} className="px-2">
                  <Button>
                    {buttonName === "Market Simulator" && (
                      <SparklesIcon className="h-5 w-5 mr-1" />
                    )}
                    <Link href={buttonActionLink}>{buttonName}</Link>
                  </Button>
                </div>
              );
            })}

          {customActionDetails &&
            customActionDetails.map((customAction, idx) => {
              const { customButton } = customAction;
              return <div key={idx}>{customButton}</div>;
            })}
        </div>
      </div>

      <div>
        {tabDetails.map((tabDetail, idx) => {
          const { tabContentName, contentDetails } = tabDetail;
          return (
            <div key={idx}>
              {tabContent === tabContentName && contentDetails}
            </div>
          );
        })}
      </div>
    </>
  );

  return (
    <div className="flex relative p-6 flex-col gap-4 w-full text-dark bg-white border border-card-border rounded-xl font-roboto overflow-hidden">
      {/* { isTimerActive && (
        <div className="loading-container absolute w-full h-3 bg-[#1e1b4b] top-0 left-0">
            <div className="loader bg-gradient-to-r from-[#282362] to-[#9f9fc4] h-full w-1/4 rounded-md animate-slide"></div>
        </div>
      )} */}
      {cardDetailTop}

      {!collapsed &&
        (loadingCondition ? (
          loadingCondition === true ? (
            <Loading />
          ) : (
            tabContentComponent
          )
        ) : (
          tabContentComponent
        ))}
    </div>
  );
};

export default OverviewCard;
