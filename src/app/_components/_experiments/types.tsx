interface Run {
  id: string;
  name: string;
  state: string;
  created_at: Date;
  question: string;
  task_count: number;
  confidence?: string;
  survey_prompt: string;
  amce_filename: string;
  amce?: Blob;
  is_private: boolean;
  r_squared: number;
  sample_size: number;
  tasks_per_respondent: number;
  total_number_of_tasks: number;
  expr_llm_model: string;
  why_prompt?: string;
  confidence_level?: string;
}

export type { Run };
