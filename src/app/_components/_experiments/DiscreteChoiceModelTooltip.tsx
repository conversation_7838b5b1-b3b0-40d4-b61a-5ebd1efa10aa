"use client";

import { Info, X } from "lucide-react";
import { useState } from "react";
import {
  Tooltip,
  Toolt<PERSON>Content,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function DiscreteChoiceModelTooltip() {
  const [open, setOpen] = useState(false);

  return (
    <TooltipProvider>
      <Tooltip open={open} onOpenChange={setOpen}>
        <TooltipTrigger asChild>
          <button
            className="inline-flex items-center justify-center ml-1 text-muted-foreground hover:text-foreground"
            aria-label="Information about R-squared values"
            onClick={() => setOpen(true)}
          >
            <Info className="h-4 w-4" />
          </button>
        </TooltipTrigger>
        <TooltipContent
          className="w-[380px] p-0 bg-[#2d2e61] text-white border-[#363a45]"
          align="center"
          side="right"
          sideOffset={5}
          avoidCollisions={true}
          onEscapeKeyDown={() => setOpen(false)}
          onPointerDownOutside={() => setOpen(false)}
        >
          <div className="relative p-5">
            <div className="space-y-4 text-[14px]">
              <p>
                For a Discrete Choice Model in published research, a reasonable
                R-squared (Variance explained) value typically ranges from 20%
                to 40%.
              </p>

              <p>
                Unlike linear regression where R-squared of 70%+ might be
                considered good, choice models have naturally lower R- squared
                values because they predict probabilities for discrete outcomes
                rather than continuous outcomes.
              </p>

              <div className="space-y-2">
                <p className="font-medium">Some key points:</p>
                <ul className="space-y-1.5">
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>
                      Values between 20%-30% are generally considered good
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Values between 30%-40% are considered excellent</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Values above 40% are rare in practice</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
