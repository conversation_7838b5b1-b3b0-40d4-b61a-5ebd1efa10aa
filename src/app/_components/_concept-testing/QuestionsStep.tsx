import React, { useContext, useState, useRef } from "react";
import ConceptTestingContext, {
  QuestionWithScale,
} from "./ConceptTestingContext";
import { But<PERSON> } from "@/components/ui/button";
import { Header } from "../_ui/NotificationCenter";
import { Input } from "@/components/ui/input";
import {
  PlusIcon,
  Trash2Icon,
  UploadIcon,
  DownloadIcon,
  XIcon,
  Loader2,
  AlertCircle,
} from "lucide-react";
import Tooltip from "../_util/ToolTip";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import { Alert, AlertDescription } from "@/components/ui/alert";
import NavigationButtons from "./NavigationButtons";
import LoadingIndicator from "../_util/LoadingIndicator";

interface LikertLabelResponseItem {
  labels: string[];
  statement: string;
}

interface QuestionsStepProps {
  onComplete: () => void;
  onBack: () => void;
}

const QuestionsStep: React.FC<QuestionsStepProps> = ({
  onComplete,
  onBack,
}) => {
  const context = useContext(ConceptTestingContext);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [fileError, setFileError] = useState<string | null>(null);
  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);
  const [isLoadingNext, setIsLoadingNext] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  if (!context) {
    console.error("QuestionsStep must be used within ConceptTestingContext");
    return null;
  }

  const {
    questions,
    setQuestions,
    currStep,
    setCurrStep,
    steps,
    description,
    imageS3Key,
  } = context;

  const handleQuestionChange = (id: string, value: string) => {
    setQuestions(
      questions.map((q) =>
        q.id === id ? { ...q, text: value, scale: undefined } : q
      )
    );
    // Clear API error when user makes changes
    if (apiError) {
      setApiError(null);
      setRetryCount(0);
    }
  };

  const handleAddQuestion = () => {
    const lastQuestion = questions[questions.length - 1];
    if (
      questions.length === 0 ||
      (lastQuestion && lastQuestion.text.trim() !== "")
    ) {
      setQuestions([...questions, { id: uuidv4(), text: "" }]);
    }
  };

  const handleRemoveQuestion = (id: string) => {
    if (questions.length > 1) {
      setQuestions(questions.filter((q) => q.id !== id));
    } else if (questions.length === 1) {
      setQuestions([{ id: uuidv4(), text: "" }]);
    }
  };

  const handleDownloadSampleCSV = () => {
    const link = document.createElement("a");
    link.href = "/concept_statements_sample.csv";
    link.download = "concept_statements_sample.csv";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleFileSelectClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setFileError(null);
    setUploadedFileName(null);

    if (!file.name.toLowerCase().endsWith(".csv")) {
      setFileError("Please upload a valid CSV file.");
      if (fileInputRef.current) fileInputRef.current.value = "";
      return;
    }

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const text = e.target?.result as string;
        if (!text) {
          throw new Error("File content is empty.");
        }

        const rows = text.split(/\r?\n/).filter((row) => row.trim() !== "");
        if (rows.length < 2) {
          throw new Error(
            "CSV must contain a header row and at least one question."
          );
        }

        const headers = rows[0].split(",").map((h) => h.trim().toLowerCase());
        const questionColIndex = headers.indexOf("questions");
        const statementColIndex = headers.indexOf("statement");
        const scaleColIndex = headers.indexOf("scale");

        if (questionColIndex === -1 && statementColIndex === -1) {
          throw new Error(
            "CSV format incorrect. Missing required 'Questions' or 'Statement' column header."
          );
        }

        const extractedQuestions: QuestionWithScale[] = rows
          .slice(1)
          .map((row) => {
            // Parse CSV properly, handling quoted fields that may contain commas
            const parseCSVRow = (csvRow: string): string[] => {
              const result = [];
              let current = "";
              let inQuotes = false;

              for (let i = 0; i < csvRow.length; i++) {
                const char = csvRow[i];
                const nextChar = csvRow[i + 1];

                if (char === '"') {
                  if (inQuotes && nextChar === '"') {
                    // Handle escaped quotes ("")
                    current += '"';
                    i++; // Skip next quote
                  } else {
                    // Toggle quote state
                    inQuotes = !inQuotes;
                  }
                } else if (char === "," && !inQuotes) {
                  // End of field
                  result.push(current.trim());
                  current = "";
                } else {
                  current += char;
                }
              }

              // Add last field
              result.push(current.trim());
              return result;
            };

            const columns = parseCSVRow(row);

            let text = "";
            let scale: string[] | undefined = undefined;
            if (statementColIndex !== -1) {
              text = columns[statementColIndex]?.trim() || "";
              if (scaleColIndex !== -1) {
                // Clean and parse scale data
                let scaleRaw = columns[scaleColIndex]?.trim() || "";

                // Remove outer quotes if present
                if (
                  (scaleRaw.startsWith('"') && scaleRaw.endsWith('"')) ||
                  (scaleRaw.startsWith("'") && scaleRaw.endsWith("'"))
                ) {
                  scaleRaw = scaleRaw.slice(1, -1);
                }

                // Remove brackets if present
                if (scaleRaw.startsWith("[") && scaleRaw.endsWith("]")) {
                  scaleRaw = scaleRaw.slice(1, -1);
                }

                // Split and clean individual scale items
                if (scaleRaw) {
                  scale = scaleRaw
                    .split(",")
                    .map((s) => {
                      let cleaned = s.trim();
                      // Remove quotes from individual items
                      if (
                        (cleaned.startsWith('"') && cleaned.endsWith('"')) ||
                        (cleaned.startsWith("'") && cleaned.endsWith("'"))
                      ) {
                        cleaned = cleaned.slice(1, -1);
                      }
                      return cleaned;
                    })
                    .filter((item) => item.length > 0);

                  if (scale.length === 0) scale = undefined;
                }
              }
            } else if (questionColIndex !== -1) {
              text = columns[questionColIndex]?.trim() || "";
            }
            return { id: uuidv4(), text, scale };
          })
          .filter((q) => q.text !== "");

        if (extractedQuestions.length === 0) {
          throw new Error(
            "No valid questions found under the 'Questions' or 'Statement' column."
          );
        }

        setQuestions(extractedQuestions);
        setUploadedFileName(file.name);
        setFileError(null);
      } catch (error: any) {
        console.error("Error processing CSV:", error);
        setFileError(error.message || "Failed to process CSV file.");
        setUploadedFileName(null);
        setQuestions([{ id: uuidv4(), text: "" }]);
      }
      if (fileInputRef.current) fileInputRef.current.value = "";
    };

    reader.onerror = () => {
      console.error("Error reading file");
      setFileError("Failed to read the file.");
      setUploadedFileName(null);
      setQuestions([{ id: uuidv4(), text: "" }]);
      if (fileInputRef.current) fileInputRef.current.value = "";
    };

    reader.readAsText(file);
  };

  const handleRemoveFile = () => {
    setUploadedFileName(null);
    setFileError(null);
    setQuestions([{ id: uuidv4(), text: "" }]);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const generateAndStoreLabels = async (): Promise<boolean> => {
    // If all questions already have a non-empty scale, skip API call
    const allHaveScale = questions.every(
      (q) => Array.isArray(q.scale) && q.scale.length > 0
    );
    if (allHaveScale) {
      return true;
    }

    // Only generate for questions missing a scale
    const questionsNeedingScale = questions.filter(
      (q) => !Array.isArray(q.scale) || q.scale.length === 0
    );
    const statements = questionsNeedingScale
      .map((q) => q.text)
      .filter((text) => text.trim() !== "");
    if (statements.length === 0 || !description || !imageS3Key) {
      setApiError(
        "Missing required data for label generation. Please ensure you have an image, description, and at least one question."
      );
      return false;
    }

    try {
      setApiError(null); // Clear any previous errors

      const payload = {
        description: description,
        image_name: imageS3Key,
        scale: "5",
        statements: statements,
      };

      const response = await fetch("/api/likert-label", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(
          data.details ||
            data.error ||
            `API returned ${response.status}: ${response.statusText}`
        );
      }

      // Validate the response structure
      if (!data.likert_labels || !Array.isArray(data.likert_labels)) {
        throw new Error(
          "Invalid response format from likert-label API. Expected 'likert_labels' array."
        );
      }

      // Check if we got labels for all statements
      const missingLabels = statements.filter(
        (statement) =>
          !data.likert_labels.some(
            (item: LikertLabelResponseItem) => item.statement === statement
          )
      );

      if (missingLabels.length > 0) {
        throw new Error(
          `API did not return labels for: ${missingLabels.join(", ")}`
        );
      }

      // Only update questions that were missing a scale
      const updatedQuestions = questions.map((q) => {
        if (Array.isArray(q.scale) && q.scale.length > 0) {
          return q; // Keep existing scale
        }

        const foundLabel = (
          data.likert_labels as LikertLabelResponseItem[]
        ).find((labelItem) => labelItem.statement === q.text);

        if (
          foundLabel &&
          Array.isArray(foundLabel.labels) &&
          foundLabel.labels.length > 0
        ) {
          return {
            ...q,
            scale: foundLabel.labels,
          };
        } else {
          // This should not happen if our validation above worked
          throw new Error(`No valid labels found for statement: "${q.text}"`);
        }
      });

      setQuestions(updatedQuestions);
      setRetryCount(0); // Reset retry count on success
      return true;
    } catch (error: any) {
      console.error("Error generating labels:", error);
      const errorMessage =
        error.message || "Failed to generate question scales";
      setApiError(errorMessage);
      setRetryCount((prev) => prev + 1);
      toast.error(`Label Generation Failed: ${errorMessage}`);
      return false;
    }
  };

  const handleNext = async () => {
    const validQuestions = questions.filter((q) => q.text.trim() !== "");
    if (validQuestions.length === 0) {
      toast.error("Please enter or upload at least one question.");
      setQuestions([{ id: uuidv4(), text: "" }]);
      return;
    }
    if (!description || !imageS3Key) {
      toast.error(
        "Missing concept description or image information. Please go back."
      );
      return;
    }

    // If there's an existing API error, allow user to proceed without retrying
    if (apiError) {
      const questionsWithoutScales = questions.filter(
        (q) => q.text.trim() !== "" && (!q.scale || q.scale.length === 0)
      );

      if (questionsWithoutScales.length > 0) {
        toast.warning(
          `Proceeding with ${questionsWithoutScales.length} question(s) without generated scales. You can add scales manually later.`
        );
      }
      onComplete();
      return;
    }

    // Try to generate labels for questions that don't have them
    setIsLoadingNext(true);
    const labelsGenerated = await generateAndStoreLabels();
    setIsLoadingNext(false);

    if (labelsGenerated) {
      onComplete();
    }
    // If labels failed to generate, user will see error and can retry or proceed
  };

  const handleRetry = async () => {
    setIsLoadingNext(true);
    const labelsGenerated = await generateAndStoreLabels();
    setIsLoadingNext(false);

    if (labelsGenerated) {
      onComplete();
    }
  };

  const handleBack = () => {
    onBack();
  };

  return (
    <div className="flex flex-col gap-6 max-w-4xl w-full mx-auto">
      {/* Loading Indicator */}
      {isLoadingNext && (
        <div className="w-full justify-start flex pl-20">
          <LoadingIndicator
            message="Generating question scales... Please hold on for a moment."
            size="md"
          />
        </div>
      )}

      <div className="flex flex-col gap-6 max-w-3xl mx-auto">
        <h2 className="text-xl font-semibold text-text-default">
          Add Questions / Statements
        </h2>
        <p className="text-text-light mb-1">
          What questions or statements do you want to ask your target audience
          about this concept? Enter manually or upload a CSV file.
        </p>
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 p-4 border border-dashed border-border rounded-lg bg-secondary-grey/30">
          <div className="flex-1 flex flex-col">
            <Button
              variant="outline"
              onClick={handleFileSelectClick}
              className="w-full sm:w-auto justify-center bg-white"
            >
              <UploadIcon className="h-4 w-4 mr-2" />
              Upload Questions CSV
            </Button>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileUpload}
              accept=".csv"
              className="hidden"
              aria-hidden="true"
            />
            <p className="text-xs text-text-light mt-2">
              Must contain a column with the header &apos;Statement&apos; (and
              optionally &apos;Scale&apos;), or a single column with the header
              &apos;Questions&apos; for backward compatibility.
            </p>
          </div>
          <div className="relative group">
            <Button
              variant="link"
              onClick={handleDownloadSampleCSV}
              className="text-sm text-primary p-0 h-auto"
            >
              <DownloadIcon className="h-4 w-4 mr-1" />
              Download Sample CSV
            </Button>
            <Tooltip message="Download sample CSV file format" position="top" />
          </div>
        </div>
        {uploadedFileName && (
          <div className="flex items-center justify-between p-2 bg-green-100 border border-green-300 rounded-md text-sm">
            <span className="text-green-800 font-medium">
              {uploadedFileName} uploaded successfully.
            </span>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleRemoveFile}
              className="h-6 w-6 text-green-800 hover:bg-green-200"
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        )}
        {fileError && (
          <div className="flex items-center justify-between p-2 bg-red-100 border border-red-300 rounded-md text-sm">
            <span className="text-red-800 font-medium">{fileError}</span>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setFileError(null)}
              className="h-6 w-6 text-red-800 hover:bg-red-200"
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        )}
        <div className="mt-4 flex flex-col gap-4">
          <h3 className="text-base font-medium text-text-default">
            Enter Questions Manually:
          </h3>
          {questions.map((q, idx) => (
            <div key={q.id} className="flex items-center gap-3">
              <Input
                type="text"
                value={q.text}
                onChange={(e) => handleQuestionChange(q.id, e.target.value)}
                placeholder={`Question ${idx + 1}`}
                className="flex-1 text-base"
              />
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleRemoveQuestion(q.id)}
                className="text-destructive hover:bg-destructive/10"
                disabled={questions.length === 1}
                title="Remove question"
              >
                <Trash2Icon className="h-5 w-5" />
              </Button>
            </div>
          ))}
          <Button
            variant="outline"
            onClick={handleAddQuestion}
            className="mt-2 self-start text-primary border-primary hover:bg-primary/10"
            title="Add another question"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Question
          </Button>
        </div>
      </div>

      {/* API Error Display */}
      {apiError && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <div className="flex flex-col gap-2">
              <p>
                <strong>Scale Generation Failed:</strong> {apiError}
              </p>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRetry}
                  disabled={isLoadingNext}
                  className="border-red-300 text-red-700 hover:bg-red-100"
                >
                  {isLoadingNext ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Retrying...
                    </>
                  ) : (
                    `Retry${retryCount > 0 ? ` (Attempt ${retryCount + 1})` : ""}`
                  )}
                </Button>
                <span className="text-sm text-red-600">
                  You can also continue without generated scales and add them
                  manually later.
                </span>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <NavigationButtons
        onBack={handleBack}
        onNext={handleNext}
        nextDisabled={isLoadingNext}
      />
    </div>
  );
};

export default QuestionsStep;
