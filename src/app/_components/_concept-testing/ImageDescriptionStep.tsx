import React, { useContext, useEffect, useRef, useState } from "react";
import ConceptTestingContext from "./ConceptTestingContext";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import Image from "next/image";
import { Loader2, X } from "lucide-react";
import { toast } from "sonner";
import NavigationButtons from "./NavigationButtons";
import LoadingIndicator from "../_util/LoadingIndicator";

interface ImageDescriptionStepProps {
  onComplete: () => void;
}

const ImageDescriptionStep: React.FC<ImageDescriptionStepProps> = ({
  onComplete,
}) => {
  // Move all hooks before any conditional logic/early returns
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [imageUrl, setImageUrl] = React.useState<string | null>(null);

  const context = useContext(ConceptTestingContext);

  // Clean up the object URL when the component unmounts or the image changes
  useEffect(() => {
    let url: string | null = null;
    if (context?.image) {
      url = URL.createObjectURL(context.image);
      setImageUrl(url);
    } else {
      setImageUrl(null); // Clear URL if image is removed
    }

    // Cleanup function
    return () => {
      if (url) {
        URL.revokeObjectURL(url);
        setImageUrl(null); // Also clear state on cleanup
      }
    };
  }, [context?.image]); // Depend on image state

  // Early return after all hooks are called
  if (!context) {
    console.error(
      "ImageDescriptionStep must be used within ConceptTestingContext"
    );
    return null;
  }

  const {
    image,
    setImage,
    imageS3Url,
    setImageS3Url,
    imageS3Key,
    setImageS3Key,
    description,
    setDescription,
  } = context;

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      // If we already have an S3 image and we're selecting a new one,
      // delete the existing one from S3
      if (imageS3Key) {
        handleDeleteImageFromS3();
      }
      setImage(e.target.files[0]);
    }
  };

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setDescription(e.target.value);
  };

  const handleDeleteImage = async () => {
    // If the image is stored in S3, delete it
    if (imageS3Key) {
      await handleDeleteImageFromS3();
    }
    setImage(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleDeleteImageFromS3 = async () => {
    if (!imageS3Key) return;

    try {
      setIsDeleting(true);
      const response = await fetch(
        `/api/s3?key=${encodeURIComponent(imageS3Key)}`,
        {
          method: "DELETE",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to delete image from S3");
      }

      setImageS3Url(null);
      setImageS3Key(null);
    } catch (error) {
      console.error("Error deleting image from S3:", error);
      toast.error("Failed to delete image from S3");
    } finally {
      setIsDeleting(false);
    }
  };

  const uploadImageToS3 = async () => {
    if (!image) return false;

    try {
      setIsUploading(true);

      const formData = new FormData();
      formData.append("image", image);

      const response = await fetch("/api/s3", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Failed to upload image to S3");
      }

      const data = await response.json();

      setImageS3Url(data.imageUrl);
      setImageS3Key(data.key);

      return true;
    } catch (error) {
      console.error("Error uploading image to S3:", error);
      toast.error("Failed to upload image to S3");
      return false;
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="relative min-h-screen flex flex-col bg-background w-full">
      {/* Scrollable Content */}
      {isUploading && (
        <div className="w-full justify-start flex pl-32">
          <LoadingIndicator
            message="Uploading image to S3... Please hold on for a moment."
            size="md"
          />
        </div>
      )}
      <div className="flex-1 overflow-auto px-0 sm:px-8 md:px-16 lg:px-32 py-8">
        {/* Main Content */}
        <div className="flex flex-col gap-12 items-start justify-center w-full max-w-4xl mx-auto">
          {/* Image Upload Section */}
          <div className="flex-1 flex flex-col gap-6 w-full">
            <Label htmlFor="concept-image" className="mb-2 text-base">
              Upload Image
            </Label>
            <Input
              id="concept-image"
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              ref={fileInputRef}
              className="max-w-xs"
              disabled={isUploading || isDeleting}
            />
            {imageUrl && (
              <div className="relative mt-2 w-full flex justify-center group">
                <div className="relative max-w-full max-h-96 w-auto h-auto">
                  <Image
                    src={imageUrl}
                    alt="Preview"
                    width={800}
                    height={600}
                    className="max-w-full max-h-96 w-auto h-auto object-contain border rounded-lg shadow"
                    style={{
                      width: "auto",
                      height: "auto",
                      maxWidth: "100%",
                      maxHeight: "24rem",
                    }}
                  />
                  <button
                    onClick={handleDeleteImage}
                    className="absolute top-2 right-2 bg-black bg-opacity-50 rounded-full p-1 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 hover:bg-opacity-70"
                    aria-label="Remove image"
                    disabled={isUploading || isDeleting}
                  >
                    {isDeleting ? (
                      <Loader2
                        className="h-5 w-5 animate-spin"
                        strokeWidth={2}
                      />
                    ) : (
                      <X className="h-5 w-5" strokeWidth={2} />
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
          {/* Description Section */}
          <div className="flex-1 flex flex-col gap-6 w-full">
            <Label htmlFor="concept-description" className="mb-2 text-base">
              Description
            </Label>
            <Textarea
              id="concept-description"
              value={description}
              onChange={handleDescriptionChange}
              placeholder="Enter a description for your concept..."
              className="min-h-[180px] text-base"
            />
          </div>
        </div>
      </div>
      <NavigationButtons
        onBack={() => {}}
        onNext={async () => {
          if (image && description.trim()) {
            if (!imageS3Url) {
              const uploadSuccess = await uploadImageToS3();
              if (!uploadSuccess) {
                return;
              }
            }
            onComplete();
          } else {
            toast.error("Please provide both an image and a description.");
          }
        }}
        nextDisabled={
          !image || !description.trim() || isUploading || isDeleting
        }
        backLabel=""
      />
    </div>
  );
};

export default ImageDescriptionStep;
