/* eslint-disable prettier/prettier */
"use client";
import { useEffect, useState, useRef } from "react";
import React from "react";
import { useRouter } from "next/navigation";
import {
  ChevronUp,
  Clock,
  Calendar,
  Users,
  Target,
  BarChart2,
  Check,
  MessageSquare,
  Image as ImageIcon,
} from "lucide-react";
import { Run } from "../_experiments/types";
import { useUser } from "@auth0/nextjs-auth0/client";
import { Badge } from "@/components/ui/badge";
import { useSubscription } from "@/app/hooks/useSubscription";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import * as Sentry from "@sentry/react";

interface ConceptTestingOverviewCardProps {
  run: Run;
  runCount: number;
  onPrivacyToggle: (id: string, isPrivate: boolean) => void;
  quickRefresh: () => Promise<void>;
}

const ConceptTestingOverviewCard = ({
  run,
  runCount,
  onPrivacyToggle,
  quickRefresh,
}: ConceptTestingOverviewCardProps) => {
  const { user } = useUser();
  const { subscriptionStatus, roles } = useSubscription();
  const router = useRouter();
  const [isToggled, setIsToggled] = useState(run.is_private);
  const [timer, setTimer] = useState<number>(0);
  const [isTimerActive, setIsTimerActive] = useState<boolean>(false);
  
  const timerRef = useRef<{
    startTimestamp: number;
    interval: any | null;
    lastKnownElapsed: number;
  }>({
    startTimestamp: 0,
    interval: null,
    lastKnownElapsed: 0,
  });

  // Timer logic for running experiments (same as ExperimentOverviewCard)
  useEffect(() => {
    if (run.state === "running") {
      const createdAtTimestamp = new Date(run.created_at).getTime();
      const initialElapsed = Math.max(
        0,
        Math.floor((Date.now() - createdAtTimestamp) / 1000)
      );
      setTimer(initialElapsed);

      timerRef.current = {
        startTimestamp: createdAtTimestamp,
        interval: null,
        lastKnownElapsed: initialElapsed,
      };

      const updateTimer = () => {
        const now = Date.now();
        const elapsed = Math.max(
          0,
          Math.floor((now - timerRef.current.startTimestamp) / 1000)
        );
        timerRef.current.lastKnownElapsed = elapsed;
        setTimer(elapsed);
      };

      const handleVisibilityChange = () => {
        if (document.visibilityState === "visible") {
          if (timerRef.current.interval) {
            clearInterval(timerRef.current.interval);
          }
          updateTimer();
          timerRef.current.interval = setInterval(updateTimer, 1000);
        } else {
          if (timerRef.current.interval) {
            clearInterval(timerRef.current.interval);
            timerRef.current.interval = null;
          }
        }
      };

      updateTimer();
      if (document.visibilityState === "visible") {
        timerRef.current.interval = setInterval(updateTimer, 1000);
      }

      document.addEventListener("visibilitychange", handleVisibilityChange);
      window.addEventListener("focus", updateTimer);
      window.addEventListener("pageshow", updateTimer);

      setIsTimerActive(true);

      return () => {
        setIsTimerActive(false);
        document.removeEventListener("visibilitychange", handleVisibilityChange);
        window.removeEventListener("focus", updateTimer);
        window.removeEventListener("pageshow", updateTimer);

        if (timerRef.current.interval) {
          clearInterval(timerRef.current.interval);
          timerRef.current.interval = null;
        }
      };
    }

    return () => {
      setIsTimerActive(false);
      if (timerRef.current.interval) {
        clearInterval(timerRef.current.interval);
        timerRef.current.interval = null;
      }
    };
  }, [run.state, run.created_at]);

  // Privacy toggle handler (same as ExperimentOverviewCard)
  const handleToggleChange = async () => {
    if (
      subscriptionStatus === "Active" ||
      roles.includes("employee") ||
      roles.includes("customer") ||
      runCount <= 2
    ) {
      const newToggledState = !isToggled;
      setIsToggled(newToggledState);

      try {
        const updatedPrivacyStatus = await updatePrivacyStatus(
          run.id,
          newToggledState
        );
        setIsToggled(updatedPrivacyStatus);
        onPrivacyToggle(run.id, updatedPrivacyStatus);
      } catch (error) {
        console.error("Error updating privacy status:", error);
        setIsToggled(!newToggledState);
        Sentry.captureException(error);
      }
    }
  };

  const updatePrivacyStatus = async (id: string, newPrivacyStatus: boolean) => {
    try {
      const response = await fetch(`/api/runs/${id}/update_config`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          config_update: {
            set_privacy: newPrivacyStatus,
          },
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update privacy status");
      }

      const data = await response.json();
      return data.configs.experiment_design.is_private;
    } catch (error) {
      console.error("Error updating privacy status:", error);
      Sentry.captureException(error);
      throw error;
    }
  };

  // Format timer display
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  // Handle card click to navigate to concept testing page
  const handleCardClick = () => {
    if (run.state !== "running") {
      router.push(`/concept-testing/${run.id}`);
    }
  };

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div 
      className={`bg-white rounded-lg border border-input shadow-sm p-6 transition-all duration-200 ${
        run.state !== "running" ? "cursor-pointer hover:shadow-md hover:border-primary/20" : ""
      }`}
      onClick={handleCardClick}
    >
      {/* Header */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="text-lg font-semibold text-text-dark line-clamp-1">
              {run.name || "Untitled Concept Test"}
            </h3>
            <Badge 
              variant={run.state === "finished" ? "default" : run.state === "running" ? "secondary" : "destructive"}
              className="text-xs"
            >
              {run.state === "finished" ? "Completed" : run.state === "running" ? "Running" : "Failed"}
            </Badge>
          </div>
          
          {run.question && (
            <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
              {run.question}
            </p>
          )}
        </div>

        {/* Privacy Toggle */}
        <div className="ml-4">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleToggleChange();
                  }}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    isToggled ? "bg-primary" : "bg-gray-200"
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      isToggled ? "translate-x-6" : "translate-x-1"
                    }`}
                  />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isToggled ? "Private" : "Public"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Running Timer */}
      {run.state === "running" && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2 text-blue-700">
            <Clock className="h-4 w-4" />
            <span className="text-sm font-medium">
              Running for {formatTime(timer)}
            </span>
          </div>
          <div className="mt-2 text-xs text-blue-600">
            Concept tests typically complete in 3-5 minutes
          </div>
        </div>
      )}

      {/* Metadata */}
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div className="flex items-center gap-2 text-muted-foreground">
          <Calendar className="h-4 w-4" />
          <span>{formatDate(run.created_at)}</span>
        </div>
        
        {run.sample_size > 0 && (
          <div className="flex items-center gap-2 text-muted-foreground">
            <Users className="h-4 w-4" />
            <span>{run.sample_size} respondents</span>
          </div>
        )}
        
        <div className="flex items-center gap-2 text-muted-foreground">
          <ImageIcon className="h-4 w-4" />
          <span>Concept Testing</span>
        </div>
        
        {run.state === "finished" && (
          <div className="flex items-center gap-2 text-green-600">
            <Check className="h-4 w-4" />
            <span>Ready to view</span>
          </div>
        )}
      </div>

      {/* Action hint for completed tests */}
      {run.state === "finished" && (
        <div className="mt-4 pt-4 border-t border-input">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">
              Click to view results
            </span>
            <BarChart2 className="h-4 w-4 text-primary" />
          </div>
        </div>
      )}
    </div>
  );
};

export default ConceptTestingOverviewCard;
