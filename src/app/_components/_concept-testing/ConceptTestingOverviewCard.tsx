/* eslint-disable prettier/prettier */
"use client";
import { useEffect, useState, useRef } from "react";
import React from "react";
import { useRouter } from "next/navigation";
import {
  Calendar,
  Users,
  ListTodo,
  BarChart2,
  Target,
  Image as ImageIcon,
} from "lucide-react";
import { Run } from "../_experiments/types";
import { useUser } from "@auth0/nextjs-auth0/client";
import { Badge } from "@/components/ui/badge";
import { useSubscription } from "@/app/hooks/useSubscription";
import { Spinner } from "@/app/primitives/Spinner/Spinner";
import * as Sentry from "@sentry/react";

interface ConceptTestingOverviewCardProps {
  run: Run;
  runCount: number;
  onPrivacyToggle: (id: string, isPrivate: boolean) => void;
  quickRefresh: () => Promise<void>;
}

const toggleButtonStyles = "w-11 h-6 rounded-full";

const ConceptTestingOverviewCard = ({
  run,
  runCount,
  onPrivacyToggle,
  quickRefresh,
}: ConceptTestingOverviewCardProps) => {
  const { user } = useUser();
  const { subscriptionStatus, roles } = useSubscription();
  const router = useRouter();
  const [isToggled, setIsToggled] = useState(run.is_private);
  const [timer, setTimer] = useState<number>(0);
  const [isTimerActive, setIsTimerActive] = useState<boolean>(false);

  const timerRef = useRef<{
    startTimestamp: number;
    interval: any | null;
    lastKnownElapsed: number;
  }>({
    startTimestamp: 0,
    interval: null,
    lastKnownElapsed: 0,
  });

  const isShortQuestion = run.question?.length < 120;

  // Timer logic for running concept tests (same as ExperimentOverviewCard)
  useEffect(() => {
    if (run.state === "running") {
      const createdAtTimestamp = new Date(run.created_at).getTime();
      const initialElapsed = Math.max(
        0,
        Math.floor((Date.now() - createdAtTimestamp) / 1000)
      );
      setTimer(initialElapsed);

      timerRef.current = {
        startTimestamp: createdAtTimestamp,
        interval: null,
        lastKnownElapsed: initialElapsed,
      };

      const updateTimer = () => {
        const now = Date.now();
        const elapsed = Math.max(
          0,
          Math.floor((now - timerRef.current.startTimestamp) / 1000)
        );
        timerRef.current.lastKnownElapsed = elapsed;
        setTimer(elapsed);
      };

      const handleVisibilityChange = () => {
        if (document.visibilityState === "visible") {
          if (timerRef.current.interval) {
            clearInterval(timerRef.current.interval);
          }
          updateTimer();
          timerRef.current.interval = setInterval(updateTimer, 1000);
        } else {
          if (timerRef.current.interval) {
            clearInterval(timerRef.current.interval);
            timerRef.current.interval = null;
          }
        }
      };

      updateTimer();
      if (document.visibilityState === "visible") {
        timerRef.current.interval = setInterval(updateTimer, 1000);
      }

      document.addEventListener("visibilitychange", handleVisibilityChange);
      window.addEventListener("focus", updateTimer);
      window.addEventListener("pageshow", updateTimer);

      setIsTimerActive(true);

      return () => {
        setIsTimerActive(false);
        document.removeEventListener(
          "visibilitychange",
          handleVisibilityChange
        );
        window.removeEventListener("focus", updateTimer);
        window.removeEventListener("pageshow", updateTimer);

        if (timerRef.current.interval) {
          clearInterval(timerRef.current.interval);
          timerRef.current.interval = null;
        }
      };
    }

    return () => {
      setIsTimerActive(false);
      if (timerRef.current.interval) {
        clearInterval(timerRef.current.interval);
        timerRef.current.interval = null;
      }
    };
  }, [run.state, run.created_at]);

  // Privacy toggle handler (same as ExperimentOverviewCard)
  const handleToggleChange = async () => {
    if (
      subscriptionStatus === "Active" ||
      roles.includes("employee") ||
      roles.includes("customer") ||
      runCount <= 2
    ) {
      const newToggledState = !isToggled;
      setIsToggled(newToggledState);

      try {
        const updatedPrivacyStatus = await updatePrivacyStatus(
          run.id,
          newToggledState
        );
        setIsToggled(updatedPrivacyStatus);
        onPrivacyToggle(run.id, updatedPrivacyStatus);
      } catch (error) {
        console.error("Error updating privacy status:", error);
        setIsToggled(!newToggledState);
        Sentry.captureException(error);
      }
    }
  };

  const updatePrivacyStatus = async (id: string, newPrivacyStatus: boolean) => {
    try {
      const response = await fetch(`/api/runs/${id}/update_config`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          config_update: {
            set_privacy: newPrivacyStatus,
          },
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update privacy status");
      }

      const data = await response.json();
      return data.configs.experiment_design.is_private;
    } catch (error) {
      console.error("Error updating privacy status:", error);
      Sentry.captureException(error);
      throw error;
    }
  };

  // Format timer display
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  // Handle card click to navigate to concept testing page (no dropdown, direct navigation)
  const handleCardClick = () => {
    if (run.state !== "running") {
      router.push(`/concept-testing/${run.id}`);
    }
  };

  // Card detail top section (matches ExperimentOverviewCard structure)
  const cardDetailTop = (
    <>
      <div
        className="flex flex-row gap-10 justify-between items-center hover:cursor-pointer"
        onMouseDown={handleCardClick}
      >
        <div className="flex flex-col w-full">
          <div className="flex items-center gap-2">
            {run.state === "running" && (
              <>
                <Badge className="bg-blue-50 text-blue-700 hover:bg-blue-100 border border-blue-200 text-sm px-3 py-1 rounded-md">
                  <div className="flex items-center gap-2">
                    <Spinner className="text-blue-700" size={20} />
                    <span>Time Elapsed: {formatTime(timer)}</span>
                  </div>
                </Badge>
              </>
            )}
          </div>
          <p
            className={`text-primary-dark font-roboto font-medium text-lg ${
              isShortQuestion
                ? "" // Centered for short questions
                : "text-justify" // Justified for longer ones
            }`}
          >
            {run.question || run.name || "Untitled Concept Test"}
          </p>
        </div>

        <div className="w-fit">
          {run.state === "running" ? (
            <div className="flex gap-2">
              <Badge className="bg-green-50 text-green-700 hover:bg-green-100 border border-green-200 text-sm px-3 py-1 rounded-md">
                Running
              </Badge>
            </div>
          ) : run.state === "failed" ? (
            <div className="flex gap-2">
              <Badge className="bg-red-50 text-red-700 hover:bg-red-100 border border-red-200 text-sm px-3 py-1 rounded-md">
                Failed
              </Badge>
            </div>
          ) : (
            // For concept testing, we show a "View Results" indicator instead of chevron
            <div className="flex gap-2">
              <Badge className="bg-primary-50 text-primary-700 hover:bg-primary-100 border border-primary-200 text-sm px-3 py-1 rounded-md">
                View Results
              </Badge>
            </div>
          )}
        </div>
      </div>

      <div className="flex flex-row gap-10">
        <div className="flex items-center gap-2">
          <label
            className={`${toggleButtonStyles} relative inline-flex items-center cursor-pointer`}
          >
            <input
              type="checkbox"
              id="togBtn"
              className="toggle-checkbox absolute block w-5 h-5 rounded-full appearance-none cursor-pointer transition-transform duration-200 ease-in"
              style={{
                top: "0.1rem",
                left: "0.1rem",
                transform: isToggled
                  ? "translateX(calc(100% - 0.25em))"
                  : "translateX(0)",
                backgroundColor: isToggled ? "white" : "white",
                transition:
                  "transform 0.2s ease-in-out, background-color 0.2s ease-in-out",
              }}
              checked={isToggled}
              onChange={handleToggleChange}
            />
            <span
              className="toggle-label block h-6 w-full rounded-full"
              style={{
                backgroundColor: isToggled ? "#1C1D47" : "gray",
                transition: "background-color 0.2s ease-in-out",
              }}
            ></span>
          </label>
          <p className="text-text-dark font-inter text-sm font-normal">
            Private
          </p>
        </div>

        <div className="flex flex-row gap-2">
          <Calendar />
          <p className="text-text-dark font-inter text-sm font-normal">
            {new Date(run.created_at).toLocaleDateString("default", {
              month: "long",
              day: "numeric",
              year: "numeric",
            })}
          </p>
        </div>

        <div className="flex flex-row gap-2">
          <Users />
          <p className="text-text-dark font-inter text-sm font-normal">
            Respondents: {`${run.sample_size ?? 0}` || "Pending..."}
          </p>
        </div>

        <div className="flex flex-row gap-2">
          <ListTodo />
          <p className="text-text-dark font-inter text-sm font-normal">
            Tasks per respondent:{" "}
            {`${run.tasks_per_respondent ?? 0}` || "Pending..."}
          </p>
        </div>

        <div className="flex flex-row gap-2">
          <BarChart2 />
          <p className="text-text-dark font-inter text-sm font-normal">
            Sample size: {`${run.total_number_of_tasks ?? 0}` || "Pending..."}
          </p>
        </div>

        <div className="flex flex-row gap-2">
          <ImageIcon />
          <p className="text-text-dark font-inter text-sm font-normal">
            Concept Testing
          </p>
        </div>
      </div>
    </>
  );

  return (
    <div className="flex relative p-6 flex-col gap-4 w-full text-dark bg-white border border-card-border rounded-xl font-roboto overflow-hidden">
      {cardDetailTop}
    </div>
  );
};

export default ConceptTestingOverviewCard;
