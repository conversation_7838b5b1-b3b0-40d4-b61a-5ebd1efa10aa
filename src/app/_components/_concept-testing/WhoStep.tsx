import React, { useContext, useState } from "react";
import ConceptTestingContext, {
  ConceptTestingContextInterface,
} from "./ConceptTestingContext";
import ExperimentCreationContext from "../_ideation/ExperimentCreationContext";
import WhoComponent from "../_ideation/WhoComponent";
import { ApiStatusProvider } from "../_ideation/_who/ApiStatusContext";

interface WhoStepProps {
  onComplete: () => void;
  onBack: () => void;
  whoStep: "first" | "second" | "third";
  setWhoStep: (step: "first" | "second" | "third") => void;
}

const WhoStep: React.FC<WhoStepProps> = ({
  onComplete,
  onBack,
  whoStep,
  setWhoStep,
}) => {
  const context = useContext(
    ConceptTestingContext
  ) as ConceptTestingContextInterface;

  if (!context) {
    console.error("WhoStep must be used within ConceptTestingContext");
    return null;
  }

  const {
    populationTraits,
    setPopulationTraits,
    when,
    where,
    currStep,
    setCurrStep,
    steps,
    description,
    questions,
    selectedState,
    previousStep,
  } = context;

  // Ensure correct types for WhoComponent props
  const existingYear: string | null = when ?? null;
  const existingCountry: string | null =
    where && where.name ? where.name : null;
  const existingQuestion: string | null =
    questions.length > 0 && questions[0].text ? questions[0].text : null;

  // Map ConceptTestingContext to ExperimentCreationContext format
  const experimentContextValue = {
    // Core ideation fields that WhoComponent needs
    question: context.question,
    focus: "", // Not used in concept testing
    activeSpecialist: context.activeSpecialist,
    setActiveSpecialist: context.setActiveSpecialist,

    // Traits and attributes
    displayTraits: context.displayTraits,
    setDisplayTraits: context.setDisplayTraits,
    populationTraits: context.populationTraits,
    setPopulationTraits: context.setPopulationTraits,
    displayAttributes: context.displayAttributes,
    setDisplayAttributes: context.setDisplayAttributes,
    realWorlBrandAttributeCombinations:
      context.realWorlBrandAttributeCombinations,
    setRealWorldBrandAttributeCombinations:
      context.setRealWorldBrandAttributeCombinations,

    // When/Where
    when: context.when,
    setWhen: context.setWhen,
    where: context.where,
    setWhere: context.setWhere,
    selectedState: context.selectedState,
    setSelectedState: context.setSelectedState,

    // Other required fields
    selectedLlmModel: context.selectedLlmModel,
    setSelectedLlmModel: context.setSelectedLlmModel,
    validatedQuestions: context.validatedQuestions,
    setValidatedQuestions: context.setValidatedQuestions,
    personas: context.personas,
    setPersonas: context.setPersonas,
    specialistTraits: context.specialistTraits,
    setSpecialistTraits: context.setSpecialistTraits,
    selectedSection: context.selectedSection,
    setSelectedSection: context.setSelectedSection,
    productExists: context.productExists,
    setProductExists: context.setProductExists,
    fileState: context.fileState,
    setFileState: context.setFileState,

    // Ideation-specific fields (defaults/no-ops)
    setQuestion: () => {},
    setFocus: () => {},
    causalQuestions: [],
    setCausalQuestions: () => {},
    queryHistory: [],
    setQueryHistory: () => {},
    lastSubmittedQuery: "",
    setLastSubmittedQuery: () => {},
    showHistory: false,
    setShowHistory: () => {},
  };

  return (
    <ApiStatusProvider>
      <ExperimentCreationContext.Provider value={experimentContextValue}>
        <WhoComponent
          onComplete={onComplete}
          onBack={onBack}
          existingYear={existingYear}
          existingCountry={existingCountry}
          existingQuestion={existingQuestion}
          setPopulationTraits={setPopulationTraits}
          previousStep={previousStep}
          currentStep={whoStep}
          setCurrentStep={setWhoStep}
        />
      </ExperimentCreationContext.Provider>
    </ApiStatusProvider>
  );
};

export default WhoStep;
