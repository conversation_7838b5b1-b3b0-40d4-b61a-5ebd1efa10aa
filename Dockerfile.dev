# Use an official Node runtime as a parent image
FROM node:18-alpine

# Set the working directory in the container
WORKDIR /app

# Copy package.json to the working directory
COPY package.json ./

# Copy the current directory contents into the container at /app
COPY . .

# Run the code formatting linting script
RUN npm run format
RUN npm run lint

RUN npm run test

# Start the Next.js app
CMD ["npm", "run", "start"]