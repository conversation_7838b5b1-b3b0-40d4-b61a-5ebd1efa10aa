# Use the official Node.js 18 Alpine image as the base image
FROM node:18-alpine

# Set the working directory inside the container to /app
WORKDIR /app

# Define an argument for the Doppler token
ARG DOPPLER_TOKEN_ARG

# Set the Doppler token as an environment variable using the argument value
ENV DOPPLER_TOKEN=${DOPPLER_TOKEN_ARG}

# Install the Doppler CLI in the container
RUN wget -q -t3 'https://packages.doppler.com/public/cli/rsa.8004D9FF50437357.key' -O /etc/apk/keys/<EMAIL> && \
    echo 'https://packages.doppler.com/public/cli/alpine/any-version/main' | tee -a /etc/apk/repositories && \
    apk add doppler

# Copy the package.json and application files to the working directory in the container

COPY package.json ./
COPY . .

# Install the application dependencies specified in package.json
RUN npm install

# Run the code formatting linting script
RUN npm run format
RUN npm run lint

RUN npm run test

# Build the application using the environment variables fetched from Doppler
RUN doppler run -- npm run build

# Set the command to start the application when the container runs
CMD ["doppler", "run", "--", "npm", "run", "start"]